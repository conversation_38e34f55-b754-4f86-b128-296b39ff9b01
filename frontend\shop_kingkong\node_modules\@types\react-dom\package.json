{"name": "@types/react-dom", "version": "19.1.9", "description": "TypeScript definitions for react-dom", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-dom", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://asana.com"}, {"name": "AssureSign", "url": "http://www.assuresign.com"}, {"name": "Microsoft", "url": "https://microsoft.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/MartynasZilinskas"}, {"name": "<PERSON>", "githubUsername": "theruther4d", "url": "https://github.com/theruther4d"}, {"name": "<PERSON>", "githubUsername": "Jessidhia", "url": "https://github.com/Jessidhia"}, {"name": "<PERSON>", "githubUsername": "eps1lon", "url": "https://github.com/eps1lon"}], "main": "", "types": "index.d.ts", "exports": {".": {"types": {"default": "./index.d.ts"}}, "./client": {"types": {"default": "./client.d.ts"}}, "./canary": {"types": {"default": "./canary.d.ts"}}, "./server": {"types": {"default": "./server.d.ts"}}, "./server.browser": {"types": {"default": "./server.browser.d.ts"}}, "./server.bun": {"types": {"default": "./server.bun.d.ts"}}, "./server.edge": {"types": {"default": "./server.edge.d.ts"}}, "./server.node": {"types": {"default": "./server.node.d.ts"}}, "./static": {"types": {"default": "./static.d.ts"}}, "./static.browser": {"types": {"default": "./static.browser.d.ts"}}, "./static.edge": {"types": {"default": "./static.edge.d.ts"}}, "./static.node": {"types": {"default": "./static.node.d.ts"}}, "./experimental": {"types": {"default": "./experimental.d.ts"}}, "./test-utils": {"types": {"default": "./test-utils/index.d.ts"}}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-dom"}, "scripts": {}, "dependencies": {}, "peerDependencies": {"@types/react": "^19.0.0"}, "typesPublisherContentHash": "7dbb3f6967ec6ea50b0b724a9bb3c8d7c7b80b27e0134b57296d46f7b9291d18", "typeScriptVersion": "5.2"}