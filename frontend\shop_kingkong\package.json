{"name": "shop_kingkong", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^2.9.0", "antd": "^5.27.1", "axios": "^1.11.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icon": "^1.0.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.8.2", "react-toastify": "^11.0.5", "redux-persist": "^6.0.0", "redux-thunk": "^3.1.0"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^7.1.2"}}