{"name": "react-router-dom", "version": "7.8.2", "description": "Declarative routing for React web applications", "keywords": ["react", "router", "route", "routing", "history", "link"], "repository": {"type": "git", "url": "https://github.com/remix-run/react-router", "directory": "packages/react-router-dom"}, "license": "MIT", "author": "Remix Software <<EMAIL>>", "sideEffects": false, "main": "./dist/main.js", "unpkg": "./dist/umd/react-router-dom.production.min.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"node": {"types": "./dist/index.d.ts", "module-sync": "./dist/index.mjs", "default": "./dist/index.js"}, "import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "./package.json": "./package.json"}, "wireit": {"build": {"command": "tsup", "files": ["*.ts", "tsconfig.json", "package.json"], "output": ["dist/**"]}}, "dependencies": {"react-router": "7.8.2"}, "devDependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "tsup": "^8.3.0", "typescript": "^5.1.6", "wireit": "0.14.9"}, "peerDependencies": {"react": ">=18", "react-dom": ">=18"}, "files": ["dist/", "LICENSE.md", "README.md"], "engines": {"node": ">=20.0.0"}, "scripts": {"build": "wireit", "typecheck": "tsc"}}